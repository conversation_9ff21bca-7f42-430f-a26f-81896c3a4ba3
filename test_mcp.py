#!/usr/bin/env python3
"""
Script de diagnóstico para verificar que el MCP funcione correctamente
"""

import json
import subprocess
import sys
import os
import time

def test_ollama():
    """Verificar que Ollama esté funcionando"""
    print("🔍 Verificando Ollama...")
    
    try:
        # Verificar que ollama esté instalado
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama está instalado")
            print("📋 Modelos disponibles:")
            print(result.stdout)
            
            # Verificar modelos específicos
            models = result.stdout
            if 'llama3.1:8b' in models:
                print("✅ Modelo llama3.1:8b encontrado")
            else:
                print("❌ Modelo llama3.1:8b NO encontrado")
                print("💡 Ejecuta: ollama pull llama3.1:8b")
                
            if 'nomic-embed-text' in models:
                print("✅ Modelo nomic-embed-text encontrado")
            else:
                print("❌ Modelo nomic-embed-text NO encontrado")
                print("💡 Ejecuta: ollama pull nomic-embed-text")
                
        else:
            print("❌ Ollama no está funcionando")
            print("💡 Asegúrate de que Ollama esté instalado y ejecutándose")
            
    except FileNotFoundError:
        print("❌ Ollama no está instalado")
        print("💡 Instala Ollama desde https://ollama.ai")

def test_python_packages():
    """Verificar paquetes de Python"""
    print("\n🔍 Verificando paquetes de Python...")
    
    required_packages = ['ollama', 'pixeltable', 'requests', 'python-dotenv']
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} instalado")
        except ImportError:
            print(f"❌ {package} NO instalado")
            print(f"💡 Ejecuta: pip install {package}")

def test_mcp_script():
    """Probar el script MCP directamente"""
    print("\n🔍 Probando script MCP...")
    
    try:
        # Cambiar al directorio del script
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Probar importaciones
        print("📦 Verificando importaciones...")
        import ollama
        import pixeltable as pxt
        import requests
        print("✅ Todas las importaciones exitosas")
        
        # Probar conexión con Ollama
        print("🤖 Probando conexión con Ollama...")
        try:
            response = ollama.chat(
                model='llama3.1:8b',
                messages=[{'role': 'user', 'content': 'Hola, responde solo "OK"'}],
                options={'num_predict': 10}
            )
            print("✅ Ollama responde correctamente")
            print(f"📝 Respuesta: {response['message']['content']}")
        except Exception as e:
            print(f"❌ Error con Ollama: {e}")
            
    except Exception as e:
        print(f"❌ Error en el script: {e}")

def test_mcp_protocol():
    """Probar el protocolo MCP"""
    print("\n🔍 Probando protocolo MCP...")
    
    try:
        # Simular request MCP
        test_request = {
            "method": "tools/list",
            "params": {}
        }
        
        print("📤 Enviando request de prueba...")
        print(f"Request: {json.dumps(test_request, indent=2)}")
        
        # Aquí normalmente enviarías el request al proceso MCP
        # Por ahora solo verificamos que el formato sea correcto
        print("✅ Formato de request MCP válido")
        
    except Exception as e:
        print(f"❌ Error en protocolo MCP: {e}")

def show_cursor_config():
    """Mostrar configuración para Cursor"""
    print("\n📋 Configuración para Cursor:")
    print("=" * 50)
    
    config = {
        "mcpServers": {
            "pixeltable-expert": {
                "command": "python",
                "args": ["-u", "app.py"],
                "cwd": os.path.dirname(os.path.abspath(__file__)),
                "env": {
                    "PYTHONPATH": os.path.dirname(os.path.abspath(__file__)),
                    "PYTHONUNBUFFERED": "1"
                }
            }
        }
    }
    
    print(json.dumps(config, indent=2))
    print("=" * 50)
    print("💡 Copia esta configuración en Cursor Settings > MCP")

def main():
    print("🚀 Diagnóstico del Agente Pixeltable MCP")
    print("=" * 50)
    
    test_ollama()
    test_python_packages()
    test_mcp_script()
    test_mcp_protocol()
    show_cursor_config()
    
    print("\n✨ Diagnóstico completado!")
    print("💡 Si todos los tests pasan, el agente debería funcionar en Cursor")

if __name__ == "__main__":
    main()
