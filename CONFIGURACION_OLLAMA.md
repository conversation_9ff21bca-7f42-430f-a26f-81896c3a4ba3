# Configuración de Ollama (Gratuito y Local)

## ¿Qué es Ollama?
Ollama es una herramienta que te permite ejecutar modelos de IA grandes localmente en tu computadora de forma completamente gratuita. No necesitas API keys ni pagar por tokens.

## Instalación

### 1. Instalar Ollama
Visita [https://ollama.ai](https://ollama.ai) y descarga el instalador para tu sistema operativo.

### 2. Instalar el paquete Python
```bash
pip install ollama
```

### 3. Descargar los modelos necesarios
Abre una terminal y ejecuta estos comandos:

```bash
# Modelo principal para chat (recomendado)
ollama pull llama3.1:8b

# Modelo para embeddings
ollama pull nomic-embed-text
```

## Modelos Disponibles (Todos Gratuitos)

### Para Chat/Completions:
- `llama3.1:8b` - <PERSON>o equilibrado (recomendado)
- `llama3.1:70b` - <PERSON><PERSON> potente pero requiere más RAM
- `llama3.2:3b` - <PERSON><PERSON> rápido, menos RAM
- `codellama:7b` - Especializado en código

### Para Embeddings:
- `nomic-embed-text` - Modelo de embeddings de texto
- `all-minilm` - Alternativa más ligera

## Verificar Instalación

```bash
# Verificar que Ollama está funcionando
ollama list

# Probar el modelo de chat
ollama run llama3.1:8b "Hola, ¿cómo estás?"

# Probar embeddings
ollama run nomic-embed-text "texto de prueba"
```

## Ventajas de Ollama

✅ **Completamente gratuito**
✅ **Funciona sin internet** (después de descargar modelos)
✅ **Privacidad total** (todo se ejecuta localmente)
✅ **Sin límites de tokens**
✅ **Sin API keys**

## Requisitos del Sistema

- **RAM mínima**: 8GB (recomendado 16GB para modelos grandes)
- **Espacio en disco**: 4-8GB por modelo
- **CPU**: Cualquier procesador moderno
- **GPU**: Opcional (acelera el procesamiento)

## Configuración Actual del Proyecto

El archivo `app.py` ahora está configurado para usar:
- **Chat**: `llama3.1:8b`
- **Embeddings**: `nomic-embed-text`

## Solución de Problemas

### Si Ollama no se encuentra:
```bash
# En Windows, asegúrate de que Ollama esté en el PATH
# O reinicia la terminal después de la instalación
```

### Si el modelo no responde:
```bash
# Verifica que el modelo esté descargado
ollama list

# Si no está, descárgalo
ollama pull llama3.1:8b
```

### Para cambiar modelos:
Edita el archivo `app.py` y cambia:
- `model='llama3.1:8b'` por el modelo que prefieras
- `model='nomic-embed-text'` por otro modelo de embeddings

## Comandos Útiles

```bash
# Ver modelos instalados
ollama list

# Eliminar un modelo
ollama rm nombre_del_modelo

# Ver información del sistema
ollama show llama3.1:8b

# Actualizar Ollama
# Descarga la nueva versión desde ollama.ai
```

¡Ahora tienes una configuración completamente gratuita para tu agente de Pixeltable!
