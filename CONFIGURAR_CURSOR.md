# 🎯 CONFIGURAR AGENTE EN CURSOR - PASO A PASO

## ✅ Estado: **SERVIDOR MCP FUNCIONANDO**

Tu agente está funcionando perfectamente:
```
✅ Ollama responde correctamente
✅ Servidor MCP iniciado
✅ 3 herramientas disponibles
✅ Base de datos configurada
```

## 🚀 CONFIGURACIÓN EN CURSOR

### **Paso 1: Abrir configuración de Cursor**

**Opción A - Settings JSON:**
1. Abre Cursor
2. Presiona `Ctrl+Shift+P`
3. <PERSON><PERSON>: "Preferences: Open Settings (JSON)"
4. Se abrirá el archivo `settings.json`

**Opción B - Interfaz gráfica:**
1. Abre Cursor
2. Presiona `Ctrl+,` (Settings)
3. Busca "MCP" en la barra de búsqueda

### **Paso 2: Agregar configuración MCP**

Agrega esta configuración al archivo JSON:

```json
{
  "mcpServers": {
    "pixeltable-expert": {
      "command": "python",
      "args": ["-u", "app.py"],
      "cwd": "c:\\Users\\<USER>\\OneDrive\\ScriptsPython\\MCP-Pixeltable",
      "env": {
        "PYTHONPATH": "c:\\Users\\<USER>\\OneDrive\\ScriptsPython\\MCP-Pixeltable",
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### **Paso 3: Reiniciar Cursor**
- Cierra Cursor completamente
- Vuelve a abrirlo

### **Paso 4: Verificar que funciona**
1. Abre el chat de Cursor (`Ctrl+L`)
2. Busca "pixeltable-expert" en la lista de herramientas
3. Si aparece, ¡está funcionando!

## 🧪 PROBAR EL AGENTE

### **Consultas de ejemplo:**
```
"¿Qué es Pixeltable?"
"¿Cómo creo una tabla en Pixeltable?"
"Muéstrame ejemplos de columnas computadas"
"¿Cómo hago búsqueda semántica?"
"Explícame las funciones UDF"
```

### **Herramientas disponibles:**
- **pixeltable_query** - Consulta completa con contexto
- **pixeltable_quick_query** - Consulta rápida
- **pixeltable_search** - Búsqueda en documentación

## 🔧 SOLUCIÓN DE PROBLEMAS

### ❌ **No aparece en Cursor:**

**1. Verificar configuración:**
```bash
# Probar que el servidor funciona
python app.py
# Debe mostrar: "Servidor MCP Pixeltable Expert iniciado"
```

**2. Verificar ruta:**
- Asegúrate de que la ruta en `cwd` sea correcta
- Debe apuntar a tu carpeta del proyecto

**3. Verificar Cursor:**
- Actualiza Cursor a la versión más reciente
- Reinicia completamente después de cambios

**4. Verificar MCP en Cursor:**
- Algunos versiones de Cursor requieren extensiones MCP
- Busca "MCP" en Extensions si no aparece en Settings

### ❌ **Error al ejecutar:**

**1. Verificar Ollama:**
```bash
ollama list
# Debe mostrar llama3.1:8b
```

**2. Verificar Python:**
```bash
python --version
# Debe ser Python 3.8+
```

**3. Verificar dependencias:**
```bash
pip install ollama pixeltable requests python-dotenv
```

## 📋 CONFIGURACIÓN ALTERNATIVA

Si la configuración JSON no funciona, prueba esta configuración manual:

**En Cursor Settings:**
- **Server Name:** `pixeltable-expert`
- **Command:** `python`
- **Arguments:** `["-u", "app.py"]`
- **Working Directory:** `c:\Users\<USER>\OneDrive\ScriptsPython\MCP-Pixeltable`

## ✨ ¡LISTO PARA USAR!

Una vez configurado correctamente:
1. El agente aparecerá en el chat de Cursor
2. Podrás hacer preguntas sobre Pixeltable
3. Tendrás acceso a toda la documentación
4. Todo funcionará offline y gratis

## 🎉 **¡Tu agente experto en Pixeltable está listo!**
