# Pixeltable Expert Agent MCP para Cursor
# pip install pixeltable requests ollama python-dotenv

import pixeltable as pxt
import requests
import re
import json
import asyncio
import ollama
from datetime import datetime
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

# Configurar cliente Ollama (gratuito y local)
# No necesita API key, se ejecuta localmente

# Crear directorio base y tablas
def setup_database():
    # Crear directorio base
    pxt.create_dir('pixeltable_docs', if_exists='ignore')
    
    # Tabla principal para documentación
    docs_table = pxt.create_table('pixeltable_docs.documentation', {
        'url': pxt.String,
        'title': pxt.String,
        'content': pxt.String,
        'doc_type': pxt.String,  # 'guide', 'tutorial', 'api', 'example'
        'category': pxt.String,  # 'datastore', 'chat', 'search', etc.
        'created_at': pxt.Timestamp
    }, if_exists='ignore')
    
    # Tabla para chunks procesados
    chunks_table = pxt.create_table('pixeltable_docs.chunks', {
        'doc_id': pxt.Int,
        'chunk_id': pxt.Int,
        'chunk_text': pxt.String,
        'chunk_size': pxt.Int,
        'section_title': pxt.String,
        'code_examples': pxt.Json
    }, if_exists='ignore')
    
    # Tabla para conversaciones del agente
    conversations_table = pxt.create_table('pixeltable_docs.conversations', {
        'session_id': pxt.String,
        'user_query': pxt.String,
        'context_chunks': pxt.Json,
        'agent_response': pxt.String,
        'timestamp': pxt.Timestamp
    }, if_exists='ignore')
    
    return docs_table, chunks_table, conversations_table

# URLs completas de documentación de Pixeltable
DOCS_URLS = {
    'changelog': [
        'https://docs.pixeltable.com/docs/changelog/product-updates.md'
    ],
    'datastore': [
        'https://docs.pixeltable.com/docs/datastore/bringing-data.md',
        'https://docs.pixeltable.com/docs/datastore/computed-columns.md',
        'https://docs.pixeltable.com/docs/datastore/custom-functions.md',
        'https://docs.pixeltable.com/docs/datastore/filtering-and-selecting.md',
        'https://docs.pixeltable.com/docs/datastore/iterators.md',
        'https://docs.pixeltable.com/docs/datastore/sampling.md',
        'https://docs.pixeltable.com/docs/datastore/tables-and-operations.md',
        'https://docs.pixeltable.com/docs/datastore/vector-database.md',
        'https://docs.pixeltable.com/docs/datastore/views.md'
    ],
    'chat_examples': [
        'https://docs.pixeltable.com/docs/examples/chat/evals.md',
        'https://docs.pixeltable.com/docs/examples/chat/memory.md',
        'https://docs.pixeltable.com/docs/examples/chat/multimodal.md',
        'https://docs.pixeltable.com/docs/examples/chat/tools.md'
    ],
    'general_examples': [
        'https://docs.pixeltable.com/docs/examples/interactive-demos.md',
        'https://docs.pixeltable.com/docs/examples/sample-apps.md',
        'https://docs.pixeltable.com/docs/examples/use-cases.md'
    ],
    'search_examples': [
        'https://docs.pixeltable.com/docs/examples/search/PDF.md',
        'https://docs.pixeltable.com/docs/examples/search/audio.md',
        'https://docs.pixeltable.com/docs/examples/search/images.md',
        'https://docs.pixeltable.com/docs/examples/search/video.md',
        'https://docs.pixeltable.com/docs/examples/search/website.md'
    ],
    'vision_examples': [
        'https://docs.pixeltable.com/docs/examples/vision/label-studio.md',
        'https://docs.pixeltable.com/docs/examples/vision/voxel51.md',
        'https://docs.pixeltable.com/docs/examples/vision/yolox.md'
    ],
    'integrations': [
        'https://docs.pixeltable.com/docs/integrations/embedding-model.md',
        'https://docs.pixeltable.com/docs/integrations/frameworks.md',
        'https://docs.pixeltable.com/docs/integrations/models.md'
    ],
    'libraries': [
        'https://docs.pixeltable.com/docs/libraries/mcp.md',
        'https://docs.pixeltable.com/docs/libraries/pixelagent.md',
        'https://docs.pixeltable.com/docs/libraries/yolox.md'
    ],
    'overview': [
        'https://docs.pixeltable.com/docs/overview/building-pixeltable-with-llms.md',
        'https://docs.pixeltable.com/docs/overview/configuration.md',
        'https://docs.pixeltable.com/docs/overview/installation.md',
        'https://docs.pixeltable.com/docs/overview/pixeltable.md',
        'https://docs.pixeltable.com/docs/overview/quick-start.md'
    ],
    'support': [
        'https://docs.pixeltable.com/docs/support/faq.md',
        'https://docs.pixeltable.com/docs/support/getting-help.md'
    ],
    'tutorials': [
        'https://docs.pixeltable.com/docs/tutorials/feature-guide.md',
        'https://docs.pixeltable.com/docs/tutorials/fundamentals.md'
    ],
    'api_reference': [
        'https://pixeltable.github.io/pixeltable/'
    ]
}

# Función para cargar documentación
def load_documentation(docs_table):
    for category, urls in DOCS_URLS.items():
        for url in urls:
            try:
                response = requests.get(url)
                if response.status_code == 200:
                    title = url.split('/')[-1].replace('.md', '')
                    docs_table.insert({
                        'url': url,
                        'title': title,
                        'content': response.text,
                        'doc_type': 'guide' if 'docs/' in url else 'example',
                        'category': category,
                        'created_at': datetime.now()
                    })
                    print(f"Cargado: {title}")
            except Exception as e:
                print(f"Error cargando {url}: {e}")

# Función para chunking inteligente
@pxt.udf
def intelligent_chunk(content: str) -> list:
    """Divide documentación en chunks inteligentes"""
    chunks = []
    sections = content.split('##')
    
    for i, section in enumerate(sections):
        if len(section.strip()) > 0:
            # Extraer ejemplos de código
            code_blocks = []
            code_pattern = r'```[\s\S]*?```'
            code_matches = re.findall(code_pattern, section)
            
            for match in code_matches:
                code_blocks.append(match.strip('```').strip())
            
            # Limpiar texto sin código para el chunk principal
            clean_text = re.sub(code_pattern, '[CODE_BLOCK]', section)
            
            chunks.append({
                'chunk_id': i,
                'chunk_text': clean_text.strip(),
                'chunk_size': len(clean_text),
                'section_title': section.split('\n')[0].strip(),
                'code_examples': code_blocks
            })
    
    return chunks

# Función de búsqueda simple (sin embeddings por ahora)
@pxt.udf
def simple_search(query: str, top_k: int = 5) -> list:
    """Búsqueda simple en la documentación"""
    try:
        docs_table = pxt.get_table('pixeltable_docs.documentation')
        # Búsqueda simple por contenido
        results = docs_table.where(
            docs_table.content.contains(query) |
            docs_table.title.contains(query)
        ).limit(top_k).collect()

        return [{'title': r['title'], 'content': r['content'][:500] + '...'} for r in results]
    except:
        return [{'title': 'Pixeltable', 'content': 'Infraestructura de datos para IA multimodal'}]

# Agente experto en Pixeltable
@pxt.udf
def pixeltable_expert_agent(user_query: str, session_id: str = 'default') -> str:
    """Agente experto en Pixeltable usando Groq"""
    
    # 1. Búsqueda de contexto relevante
    relevant_chunks = simple_search(user_query, top_k=3)
    
    # 2. Construcción del prompt
    context = ""
    for chunk in relevant_chunks:
        context += f"## {chunk['title']}\n{chunk['content']}\n\n"
    
    system_prompt = f"""Eres un experto en Pixeltable, una plataforma de infraestructura de datos declarativa para aplicaciones de IA multimodal.

Contexto relevante de la documentación:
{context}

Instrucciones:
- Responde de manera precisa y técnica
- Incluye ejemplos de código cuando sea apropiado
- Referencia las funciones y métodos correctos de Pixeltable
- Si no tienes información suficiente, indícalo claramente
- Mantén un tono profesional pero accesible
- Responde en español cuando la pregunta esté en español"""
    
    # 3. Llamada a Ollama (gratuito y local)
    try:
        response = ollama.chat(
            model='llama3.1:8b',  # Modelo gratuito local
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_query}
            ],
            options={
                'temperature': 0.1,
                'top_p': 0.9,
                'num_predict': 1500
            }
        )

        agent_response = response['message']['content']

    except Exception as e:
        agent_response = f"Error al procesar la consulta: {str(e)}"
    
    # 4. Guardar conversación
    try:
        conversations_table = pxt.get_table('pixeltable_docs.conversations')
        conversations_table.insert({
            'session_id': session_id,
            'user_query': user_query,
            'context_chunks': [chunk['title'] for chunk in relevant_chunks],
            'agent_response': agent_response,
            'timestamp': datetime.now()
        })
    except Exception as e:
        print(f"Error guardando conversación: {e}")
    
    return agent_response

# Función para consultas rápidas
@pxt.udf
def quick_pixeltable_query(user_query: str) -> str:
    """Consulta rápida usando Ollama sin contexto extenso"""

    try:
        response = ollama.chat(
            model='llama3.1:8b',  # Modelo gratuito local
            messages=[
                {
                    "role": "system",
                    "content": """Eres un experto en Pixeltable. Responde consultas técnicas de manera concisa y precisa.
                    Pixeltable es una infraestructura de datos declarativa para aplicaciones de IA multimodal."""
                },
                {"role": "user", "content": user_query}
            ],
            options={
                'temperature': 0.1,
                'num_predict': 800
            }
        )

        return response['message']['content']

    except Exception as e:
        return f"Error: {str(e)}"

# Clase para el servidor MCP
class PixeltableExpertMCP:
    def __init__(self):
        self.name = "pixeltable-expert"
        self.version = "1.0.0"
        
    async def handle_request(self, request):
        """Maneja requests del protocolo MCP"""
        method = request.get('method')
        params = request.get('params', {})
        
        if method == 'tools/list':
            return {
                "tools": [
                    {
                        "name": "pixeltable_query",
                        "description": "Consulta completa al experto en Pixeltable con contexto",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": "Pregunta sobre Pixeltable"
                                },
                                "session_id": {
                                    "type": "string",
                                    "description": "ID de sesión opcional"
                                }
                            },
                            "required": ["query"]
                        }
                    },
                    {
                        "name": "pixeltable_quick_query",
                        "description": "Consulta rápida sin contexto extenso (ultra rápida)",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": "Pregunta simple sobre Pixeltable"
                                }
                            },
                            "required": ["query"]
                        }
                    },
                    {
                        "name": "pixeltable_search",
                        "description": "Búsqueda directa en documentación de Pixeltable",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": "Términos de búsqueda"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Número máximo de resultados",
                                    "default": 5
                                }
                            },
                            "required": ["query"]
                        }
                    }
                ]
            }
        
        elif method == 'tools/call':
            tool_name = params.get('name')
            args = params.get('arguments', {})
            
            if tool_name == 'pixeltable_query':
                try:
                    response = pixeltable_expert_agent(
                        args['query'],
                        args.get('session_id', 'default')
                    )
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": str(response)
                            }
                        ]
                    }
                except Exception as e:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Error: {str(e)}"
                            }
                        ]
                    }
            
            elif tool_name == 'pixeltable_quick_query':
                try:
                    response = quick_pixeltable_query(args['query'])
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": str(response)
                            }
                        ]
                    }
                except Exception as e:
                    return {
                        "content": [
                            {
                                "type": "text",
                                "text": f"Error: {str(e)}"
                            }
                        ]
                    }
            
            elif tool_name == 'pixeltable_search':
                results = simple_search(
                    args['query'],
                    args.get('limit', 5)
                )
                return {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(results, indent=2)
                        }
                    ]
                }
        
        return {"error": "Método no soportado"}

# Función principal para inicializar y ejecutar el servidor MCP
async def main():
    print("Iniciando servidor MCP Pixeltable Expert...")

    try:
        # Configurar base de datos
        docs_table, _, _ = setup_database()
        print("Base de datos configurada correctamente")

        # Agregar documentación básica si no existe
        if docs_table.count() == 0:
            print("Agregando documentación básica...")
            docs_table.insert({
                'url': 'https://docs.pixeltable.com/docs/overview/pixeltable',
                'title': 'Pixeltable Overview',
                'content': '''Pixeltable es una infraestructura de datos declarativa para aplicaciones de IA multimodal.

Características principales:
- Tablas para datos estructurados y no estructurados
- Columnas computadas con funciones UDF
- Índices de embeddings para búsqueda semántica
- Integración con modelos de IA
- Soporte para imágenes, video, audio y texto''',
                'doc_type': 'guide',
                'category': 'overview',
                'created_at': datetime.now()
            })
            print("Documentación básica agregada")

    except Exception as e:
        print(f"Error en configuración: {e}")
        print("Continuando con configuración mínima...")

    # Iniciar servidor MCP
    server = PixeltableExpertMCP()
    print("Servidor MCP Pixeltable Expert iniciado", flush=True)

    while True:
        try:
            line = input()
            if line:
                request = json.loads(line)
                response = await server.handle_request(request)
                print(json.dumps(response), flush=True)
        except EOFError:
            break
        except Exception as e:
            print(json.dumps({"error": str(e)}), flush=True)

if __name__ == "__main__":
    # Este agente experto se convierte en tu asistente personal para Pixeltable,
    # accesible directamente desde Cursor a través del protocolo MCP.
    asyncio.run(main())
