# Configuración del Agente Pixeltable en Cursor

## 📋 Pasos de instalación:

### 1. Ejecutar setup automático
```bash
# Ejecuta el archivo batch para instalar todo:
setup_ollama.bat
```

### 2. Configurar MCP en Cursor

#### Opción A: Configuración automática
1. Abre Cursor
2. Ve a **Settings** (Ctrl+,)
3. Busca "MCP" o "Model Context Protocol"
4. Agrega la configuración del archivo `cursor_mcp_config.json`

#### Opción B: Configuración manual
1. Abre Cursor
2. Ve a **Settings** → **Extensions** → **MCP**
3. Agrega un nuevo servidor:
   - **Nombre**: `pixeltable-expert`
   - **Comando**: `python`
   - **Argumentos**: `["app.py"]`
   - **Directorio**: `c:\Users\<USER>\OneDrive\ScriptsPython\MCP-Pixeltable`

### 3. Verificar que Ollama esté funcionando
```bash
# Verificar modelos instalados
ollama list

# Probar el modelo
ollama run llama3.1:8b "Hola, explícame qué es Pixeltable"
```

## 🎯 Cómo usar el agente en Cursor:

### 1. Activar el agente
- En Cursor, abre el chat (Ctrl+L)
- El agente debería aparecer como disponible
- Selecciona "pixeltable-expert"

### 2. Ejemplos de consultas:
```
"¿Cómo creo una tabla en Pixeltable?"
"Muéstrame cómo agregar embeddings a una tabla"
"¿Cómo hago una búsqueda por similitud?"
"Explícame las funciones UDF en Pixeltable"
```

## 🔧 Solución de problemas:

### Si Ollama no responde:
```bash
# Reiniciar Ollama
ollama serve
```

### Si Cursor no encuentra el agente:
1. Verifica que `app.py` esté en la ruta correcta
2. Verifica que Python pueda ejecutar el archivo:
   ```bash
   cd c:\Users\<USER>\OneDrive\ScriptsPython\MCP-Pixeltable
   python app.py
   ```

### Si hay errores de importación:
```bash
# Reinstalar dependencias
pip install --upgrade ollama pixeltable requests python-dotenv
```

## 📊 Ventajas de esta configuración:

✅ **Completamente gratuito**
✅ **Funciona offline** (después de descargar modelos)
✅ **Privacidad total** (todo local)
✅ **Sin límites de uso**
✅ **Integrado directamente en Cursor**

## 🚀 ¡Listo para usar!

Una vez configurado, tendrás tu asistente experto en Pixeltable disponible directamente en Cursor, funcionando completamente gratis y sin necesidad de internet.
