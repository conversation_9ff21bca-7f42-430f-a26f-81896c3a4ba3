# Configuración del Agente Pixeltable en Cursor

## 📋 Pasos de instalación:

### 1. Ejecutar setup automático
```bash
# Ejecuta el archivo batch para instalar todo:
setup_ollama.bat
```

### 2. Configurar MCP en Cursor

#### Método 1: Configuración en settings.json
1. Abre Cursor
2. Presiona **Ctrl+Shift+P** y busca "Preferences: Open Settings (JSON)"
3. Agrega esta configuración al archivo JSON:

```json
{
  "mcpServers": {
    "pixeltable-expert": {
      "command": "python",
      "args": ["-u", "app.py"],
      "cwd": "c:\\Users\\<USER>\\OneDrive\\ScriptsPython\\MCP-Pixeltable",
      "env": {
        "PYTHONPATH": "c:\\Users\\<USER>\\OneDrive\\ScriptsPython\\MCP-Pixeltable",
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

#### Método 2: Configuración en la interfaz
1. Abre Cursor
2. Ve a **Settings** (Ctrl+,)
3. Busca "MCP" en la barra de búsqueda
4. Si no aparece, ve a **Extensions** y busca "MCP"
5. Configura el servidor con estos datos:
   - **Nombre**: `pixeltable-expert`
   - **Comando**: `python`
   - **Argumentos**: `["-u", "app.py"]`
   - **Directorio**: `c:\Users\<USER>\OneDrive\ScriptsPython\MCP-Pixeltable`

### 3. Ejecutar diagnóstico completo
```bash
# Ejecutar script de diagnóstico
python test_mcp.py
```

Este script verificará:
- ✅ Ollama instalado y funcionando
- ✅ Modelos descargados
- ✅ Paquetes Python instalados
- ✅ Script MCP funcional
- ✅ Configuración correcta

### 4. Verificar manualmente (opcional)
```bash
# Verificar modelos instalados
ollama list

# Probar el modelo
ollama run llama3.1:8b "Hola, explícame qué es Pixeltable"

# Probar el script directamente
python app.py
```

## 🎯 Cómo usar el agente en Cursor:

### 1. Activar el agente
- En Cursor, abre el chat (Ctrl+L)
- El agente debería aparecer como disponible
- Selecciona "pixeltable-expert"

### 2. Ejemplos de consultas:
```
"¿Cómo creo una tabla en Pixeltable?"
"Muéstrame cómo agregar embeddings a una tabla"
"¿Cómo hago una búsqueda por similitud?"
"Explícame las funciones UDF en Pixeltable"
```

## 🔧 Solución de problemas:

### ❌ El agente NO aparece en Cursor:

#### Problema 1: Configuración MCP incorrecta
```bash
# 1. Verificar que Cursor tenga soporte MCP habilitado
# 2. Reiniciar Cursor completamente
# 3. Verificar la configuración en Settings > MCP
```

#### Problema 2: Ruta incorrecta
```bash
# Verificar que la ruta sea correcta:
cd c:\Users\<USER>\OneDrive\ScriptsPython\MCP-Pixeltable
dir app.py  # Debe mostrar el archivo
```

#### Problema 3: Python no encuentra el script
```bash
# Probar manualmente:
cd c:\Users\<USER>\OneDrive\ScriptsPython\MCP-Pixeltable
python -u app.py
# Debe mostrar: "Servidor MCP Pixeltable Expert iniciado"
```

#### Problema 4: Cursor no reconoce MCP
1. **Actualizar Cursor** a la versión más reciente
2. **Verificar extensiones MCP** estén instaladas
3. **Reiniciar Cursor** después de cambios de configuración

### ❌ Si Ollama no responde:
```bash
# Reiniciar Ollama
ollama serve

# Verificar que esté corriendo
ollama list
```

### ❌ Si hay errores de importación:
```bash
# Reinstalar dependencias
pip install --upgrade ollama pixeltable requests python-dotenv
```

### ❌ Si el agente responde con errores:
```bash
# Ejecutar diagnóstico completo
python test_mcp.py

# Verificar logs de Cursor (si están disponibles)
```

## 📊 Ventajas de esta configuración:

✅ **Completamente gratuito**
✅ **Funciona offline** (después de descargar modelos)
✅ **Privacidad total** (todo local)
✅ **Sin límites de uso**
✅ **Integrado directamente en Cursor**

## 🚀 ¡Listo para usar!

Una vez configurado, tendrás tu asistente experto en Pixeltable disponible directamente en Cursor, funcionando completamente gratis y sin necesidad de internet.
