@echo off
echo ========================================
echo   INSTALACION AGENTE PIXELTABLE MCP
echo ========================================
echo.

echo [1/5] Instalando dependencias de Python...
pip install --upgrade ollama pixeltable requests python-dotenv
if %errorlevel% neq 0 (
    echo ERROR: Fallo en instalacion de paquetes Python
    pause
    exit /b 1
)

echo.
echo [2/5] Verificando Ollama...
ollama --version
if %errorlevel% neq 0 (
    echo ERROR: Ollama no esta instalado
    echo Descarga e instala Ollama desde: https://ollama.ai
    pause
    exit /b 1
)

echo.
echo [3/5] Descargando modelo principal (llama3.1:8b)...
echo (Esto puede tomar varios minutos - ~4.7GB)
ollama pull llama3.1:8b
if %errorlevel% neq 0 (
    echo ERROR: Fallo descarga de llama3.1:8b
    pause
    exit /b 1
)

echo.
echo [4/5] Descargando modelo de embeddings (nomic-embed-text)...
echo (Esto puede tomar unos minutos - ~274MB)
ollama pull nomic-embed-text
if %errorlevel% neq 0 (
    echo ERROR: Fallo descarga de nomic-embed-text
    pause
    exit /b 1
)

echo.
echo [5/5] Ejecutando diagnostico...
python test_mcp.py

echo.
echo ========================================
echo   INSTALACION COMPLETADA
echo ========================================
echo.
echo Modelos instalados:
ollama list
echo.
echo Siguiente paso:
echo 1. Configura MCP en Cursor usando cursor_mcp_config.json
echo 2. Lee INSTRUCCIONES_CURSOR.md para detalles
echo.
pause
