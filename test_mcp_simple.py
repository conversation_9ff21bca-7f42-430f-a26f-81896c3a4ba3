#!/usr/bin/env python3
"""
Script simple para probar el servidor MCP
"""

import json
import subprocess
import sys
import time

def test_mcp_server():
    """Probar el servidor MCP directamente"""
    print("🧪 Probando servidor MCP...")
    
    try:
        # Iniciar el servidor MCP
        process = subprocess.Popen(
            [sys.executable, 'app.py'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1
        )
        
        # Esperar a que se inicie
        time.sleep(3)
        
        # Probar request de herramientas
        test_request = {
            "method": "tools/list",
            "params": {}
        }
        
        print("📤 Enviando request tools/list...")
        process.stdin.write(json.dumps(test_request) + '\n')
        process.stdin.flush()
        
        # Leer respuesta
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print("✅ Respuesta recibida:")
            print(json.dumps(response, indent=2))
            
            # Verificar que las herramientas estén disponibles
            if 'tools' in response:
                tools = response['tools']
                print(f"🛠️  Herramientas disponibles: {len(tools)}")
                for tool in tools:
                    print(f"   - {tool['name']}: {tool['description']}")
            else:
                print("❌ No se encontraron herramientas en la respuesta")
        else:
            print("❌ No se recibió respuesta")
        
        # Probar una consulta rápida
        query_request = {
            "method": "tools/call",
            "params": {
                "name": "pixeltable_quick_query",
                "arguments": {
                    "query": "¿Qué es Pixeltable?"
                }
            }
        }
        
        print("\n📤 Enviando consulta de prueba...")
        process.stdin.write(json.dumps(query_request) + '\n')
        process.stdin.flush()
        
        # Leer respuesta
        response_line = process.stdout.readline()
        if response_line:
            response = json.loads(response_line.strip())
            print("✅ Respuesta de consulta:")
            if 'content' in response and response['content']:
                print(response['content'][0]['text'][:200] + "...")
            else:
                print(json.dumps(response, indent=2))
        else:
            print("❌ No se recibió respuesta a la consulta")
        
        # Terminar proceso
        process.terminate()
        process.wait(timeout=5)
        
        print("\n✅ Prueba del servidor MCP completada")
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba MCP: {e}")
        if 'process' in locals():
            process.terminate()
        return False

def test_ollama_connection():
    """Probar conexión con Ollama"""
    print("\n🤖 Probando conexión con Ollama...")
    
    try:
        import ollama
        
        # Probar una consulta simple
        response = ollama.chat(
            model='llama3.1:8b',
            messages=[{'role': 'user', 'content': 'Responde solo "OK"'}],
            options={'num_predict': 5}
        )
        
        print("✅ Ollama responde correctamente")
        print(f"📝 Respuesta: {response['message']['content']}")
        return True
        
    except Exception as e:
        print(f"❌ Error con Ollama: {e}")
        print("💡 Asegúrate de que Ollama esté instalado y el modelo llama3.1:8b descargado")
        return False

def main():
    print("🚀 Prueba Simple del Agente Pixeltable MCP")
    print("=" * 50)
    
    # Verificar Ollama
    ollama_ok = test_ollama_connection()
    
    if ollama_ok:
        # Probar servidor MCP
        mcp_ok = test_mcp_server()
        
        if mcp_ok:
            print("\n🎉 ¡Todo funciona correctamente!")
            print("💡 Ahora puedes configurar el agente en Cursor")
        else:
            print("\n⚠️  El servidor MCP tiene problemas")
    else:
        print("\n⚠️  Ollama no está funcionando correctamente")
        print("💡 Instala Ollama y descarga el modelo llama3.1:8b")
    
    print("\n📋 Siguiente paso: Configurar en Cursor usando cursor_mcp_config.json")

if __name__ == "__main__":
    main()
